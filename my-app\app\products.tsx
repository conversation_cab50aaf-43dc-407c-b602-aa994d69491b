import { useTheme } from '@/contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import getServerBaseUrl from '@/envConfig';

const backendUrl = getServerBaseUrl();

interface Product {
  _id: string;
  name: string;
  description: string;
  shortDescription?: string;
  category: string;
  brand?: string;
  price: number;
  originalPrice?: number;
  images: Array<{
    url: string;
    alt?: string;
    isPrimary: boolean;
  }>;
  primaryImage?: string;
  isActive: boolean;
}

const theme = {
  shadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  borderRadius: {
    card: 16,
    button: 12,
    image: 10,
  },
};

export default function ProductsScreen() {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { t, i18n } = useTranslation();
  const { colors } = useTheme();

  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoadingProducts(true);
      setError(null);

      try {
        const response = await fetch(`${backendUrl}api/content/products?limit=20`);
        if (!response.ok) throw new Error('Failed to fetch products');
        const data = await response.json();
        setProducts(data.data?.products || []);
      } catch (err: any) {
        console.error('Fetch error:', err);
        setError('Failed to load products. Please try again later.');
      } finally {
        setIsLoadingProducts(false);
      }
    };

    fetchProducts();
  }, []);

  const renderProductItem = ({ item }: { item: Product }) => {
    const imageUrl =
      item.primaryImage ||
      (item.images?.[0]?.url
        ? item.images[0].url.startsWith('http')
          ? item.images[0].url
          : `${backendUrl}${item.images[0].url}`
        : 'https://via.placeholder.com/110x110?text=No+Image');

    return (
      <View
        style={[
          styles.productCard,
          {
            backgroundColor: colors.card,
            borderColor: colors.border,
            borderRadius: theme.borderRadius.card,
          },
        ]}
      >
        <View style={styles.itemImageContainer}>
          <Image
            source={{ uri: imageUrl }}
            style={[styles.productImage, { borderRadius: theme.borderRadius.image }]}
            accessibilityLabel={`Product image for ${item.name}`}
          />
        </View>
        <View style={styles.itemDetailContainer}>
          <Text style={[styles.productName, { color: colors.text }]}>{item.name}</Text>
          <Text
            style={[styles.productDescription, { color: colors.textSecondary }]}
            numberOfLines={2}
          >
            {item.shortDescription || item.description}
          </Text>
          {item.price && (
            <Text style={[styles.productPrice, { color: colors.primary }]}>
              {new Intl.NumberFormat(i18n.language, {
                style: 'currency',
                currency: 'INR',
              }).format(item.price)}
              {item.originalPrice && item.originalPrice > item.price && (
                <Text style={[styles.originalPrice, { color: colors.textSecondary }]}>
                  {' '}
                  {new Intl.NumberFormat(i18n.language, {
                    style: 'currency',
                    currency: 'INR',
                  }).format(item.originalPrice)}
                </Text>
              )}
            </Text>
          )}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <FlatList
        data={products}
        keyExtractor={(item) => item._id}
        renderItem={renderProductItem}
        ListHeaderComponent={() => (
          <View style={[styles.card, { backgroundColor: colors.surface }]}>
            <View style={[styles.iconContainer, { backgroundColor: colors.secondary + '15' }]}>
              <Ionicons name="storefront" size={40} color={colors.secondary} />
            </View>
            <Text style={[styles.title, { color: colors.text }]}>{t('Our Products')}</Text>
            <Text style={[styles.description, { color: colors.textSecondary }]}>
              Explore our wide range of products and services available for redemption with your
              points.
            </Text>
          </View>
        )}
        contentContainerStyle={styles.content}
        ListEmptyComponent={() => {
          if (isLoadingProducts) {
            return (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                  Loading products...
                </Text>
              </View>
            );
          }

          if (error) {
            return (
              <Text style={[styles.emptyText, { color: colors.error || 'red' }]}>{error}</Text>
            );
          }

          return (
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No products available.
            </Text>
          );
        }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  card: {
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  loadingContainer: {
    alignItems: 'center',
    marginTop: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 16,
    marginTop: 32,
  },
  productCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
    width: '100%',
  },
  itemImageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  productImage: {
    width: 110,
    height: 110,
    marginLeft: 14,
  },
  itemDetailContainer: {
    flex: 2,
    paddingLeft: 20,
    justifyContent: 'center',
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  productDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  originalPrice: {
    fontSize: 14,
    textDecorationLine: 'line-through',
    fontWeight: 'normal',
  },
});
